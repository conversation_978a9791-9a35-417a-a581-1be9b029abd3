#!/usr/bin/env python3
"""
Script per eliminare script di evaluation obsoleti e corrotti
Mantiene solo quelli corretti e aggiornati
"""

import os
import shutil

def cleanup_evaluation_scripts():
    """Elimina script obsoleti e corrotti"""
    
    print("🧹 Pulizia script evaluation obsoleti...")
    
    # Script da eliminare (obsoleti/corrotti)
    scripts_to_remove = [
        # Script baseline obsoleti
        "scripts/evaluation/baseline_inference_hf_official.py",
        "scripts/evaluation/calculate_baseline_metrics_100.py", 
        "scripts/evaluation/calculate_metrics_with_clip.py",
        "scripts/evaluation/clip_metrics_calculator.py",
        "scripts/evaluation/create_baseline_fixed_report.py",
        "scripts/evaluation/create_baseline_qualitative_report.py",
        "scripts/evaluation/create_baseline_radar_final.py",
        "scripts/evaluation/create_final_radar_chart.py",
        "scripts/evaluation/create_final_t7_radar_chart.py",
        "scripts/evaluation/create_html_qualitative_report.py",
        "scripts/evaluation/evaluate_baseline_corrected.py",
        "scripts/evaluation/evaluate_t7_models.py",
        
        # Script gemma specifici obsoleti
        "scripts/evaluation/generate_gemma_only_report.py",
        "scripts/evaluation/generate_individual_radar_charts.py",
        "scripts/evaluation/generate_qualitative_report.py",
        "scripts/evaluation/generate_qualitative_report_with_gemma.py",
        "scripts/evaluation/generate_radar_chart_with_gemma.py",
        "scripts/evaluation/run_gemma_evaluation_pipeline.py",
        
        # Script di test e utility obsoleti
        "scripts/evaluation/open_report.py",
        "scripts/evaluation/quick_qualitative_examples.py",
        "scripts/evaluation/run_baseline_inference_colors_fixed.py",
        "scripts/evaluation/test_clip_score.py",
        "scripts/evaluation/example_usage.sh",
        
        # README duplicati
        "scripts/evaluation/README_EVALUATION.md",
    ]
    
    removed_count = 0
    
    for script_path in scripts_to_remove:
        if os.path.exists(script_path):
            try:
                os.remove(script_path)
                print(f"   ❌ Eliminato: {script_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ Errore eliminando {script_path}: {e}")
        else:
            print(f"   📂 Non trovato: {script_path}")
    
    print(f"\n✅ Eliminati {removed_count} script obsoleti")
    
    # Script mantenuti (corretti)
    kept_scripts = [
        "scripts/evaluation/evaluate_any_model.py",  # ✅ Script generico universale
        "scripts/evaluation/evaluate_trained_models_t7.py",  # ✅ Script per modelli T7
        "scripts/evaluation/calculate_gemma_metrics.py",  # ✅ Script metriche Gemma
        "scripts/evaluation/README_evaluation.md",  # ✅ Documentazione
    ]
    
    print(f"\n📋 SCRIPT MANTENUTI (CORRETTI):")
    for script in kept_scripts:
        if os.path.exists(script):
            print(f"   ✅ {script}")
        else:
            print(f"   ❌ MANCANTE: {script}")

if __name__ == "__main__":
    cleanup_evaluation_scripts()
