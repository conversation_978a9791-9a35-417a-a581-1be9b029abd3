#!/usr/bin/env python3
"""
BLIP-2 Inference con dataset CORRETTO (immagini colorate)
"""

import json
import torch
from PIL import Image
from transformers import <PERSON>lip2Processor, Blip2ForConditionalGeneration
import os
from datetime import datetime

def load_model():
    """Carica il modello BLIP-2"""
    print("🔄 Caricamento BLIP-2...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    processor = Blip2Processor.from_pretrained("Salesforce/blip2-opt-2.7b")
    model = Blip2ForConditionalGeneration.from_pretrained(
        "Salesforce/blip2-opt-2.7b",
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
    ).to(device)
    
    print("✅ BLIP-2 caricato!")
    return processor, model, device

def run_inference():
    """Esegue inferenza BLIP-2 con dataset corretto"""
    
    # Carica dataset CORRETTO
    dataset_path = "data/processed/xml_format_optimized/baseline_t7_corrected_400_REAL_colors_fixed.json"
    
    print(f"📂 Caricamento dataset CORRETTO: {dataset_path}")
    
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    print(f"✅ Dataset caricato: {len(dataset)} esempi")
    
    # Carica modello
    processor, model, device = load_model()
    
    # Risultati
    results = []
    
    print("🚀 Inizio inferenza BLIP-2...")
    
    for i, item in enumerate(dataset):
        try:
            # Carica immagine COLORATA
            image_path = item['image_path']
            
            if not os.path.exists(image_path):
                print(f"❌ Immagine non trovata: {image_path}")
                continue
            
            image = Image.open(image_path).convert('RGB')
            
            # Prepara input
            inputs = processor(image, return_tensors="pt").to(device)
            
            # Genera descrizione
            with torch.no_grad():
                generated_ids = model.generate(**inputs, max_length=50, num_beams=5)
                prediction = processor.decode(generated_ids[0], skip_special_tokens=True)
            
            # Salva risultato
            result = {
                'example_id': i,
                'image_path': image_path,
                'ground_truth': item['svg_description'],
                'prediction': prediction,
                'success': True
            }
            
            results.append(result)
            
            if (i + 1) % 50 == 0:
                print(f"   📊 Processati: {i + 1}/{len(dataset)}")
            
        except Exception as e:
            print(f"❌ Errore esempio {i}: {e}")
            
            # Salva errore
            result = {
                'example_id': i,
                'image_path': item.get('image_path', ''),
                'ground_truth': item.get('svg_description', ''),
                'prediction': '',
                'success': False,
                'error': str(e)
            }
            results.append(result)
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/blip2_REAL_colors_results_{timestamp}.json"
    
    os.makedirs("evaluation_results", exist_ok=True)
    
    output_data = {
        'model': 'BLIP-2',
        'dataset': 'baseline_t7_corrected_400_REAL_colors_fixed.json',
        'timestamp': timestamp,
        'total_examples': len(dataset),
        'successful_examples': len([r for r in results if r['success']]),
        'results': results
    }
    
    with open(output_path, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\n✅ BLIP-2 inference completata!")
    print(f"📁 Risultati salvati: {output_path}")
    print(f"📊 Esempi processati: {len([r for r in results if r['success']])}/{len(dataset)}")

if __name__ == "__main__":
    run_inference()
