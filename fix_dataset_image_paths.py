#!/usr/bin/env python3
"""
Script per correggere i path delle immagini nel dataset
Sostituisce i path delle immagini nere con quelli delle immagini colorate
"""

import json
import os

def fix_image_paths():
    """Corregge i path delle immagini nel dataset"""
    
    # Carica il dataset
    dataset_path = "data/processed/xml_format_optimized/baseline_t7_corrected_400_colors_fixed.json"
    
    print(f"📂 Caricamento dataset: {dataset_path}")
    
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    print(f"✅ Dataset caricato: {len(dataset)} esempi")
    
    # Correggi i path delle immagini
    corrected_count = 0
    
    for item in dataset:
        if 'image_path' in item:
            original_path = item['image_path']
            
            # Sostituisci il path per usare le immagini corrette
            if 'baseline_t7_images_full' in original_path:
                corrected_path = original_path.replace(
                    'baseline_t7_images_full', 
                    'baseline_t7_images_colors_fixed'
                )
                
                # Verifica che l'immagine corretta esista
                if os.path.exists(corrected_path):
                    item['image_path'] = corrected_path
                    corrected_count += 1
                else:
                    print(f"⚠️ Immagine corretta non trovata: {corrected_path}")
    
    print(f"✅ Corretti {corrected_count} path di immagini")
    
    # Salva il dataset corretto
    output_path = "data/processed/xml_format_optimized/baseline_t7_corrected_400_REAL_colors_fixed.json"
    
    with open(output_path, 'w') as f:
        json.dump(dataset, f, indent=2)
    
    print(f"💾 Dataset corretto salvato: {output_path}")
    
    # Verifica che tutte le immagini esistano
    missing_count = 0
    for item in dataset:
        if 'image_path' in item and not os.path.exists(item['image_path']):
            missing_count += 1
    
    if missing_count == 0:
        print("✅ Tutte le immagini sono presenti!")
    else:
        print(f"❌ {missing_count} immagini mancanti")

if __name__ == "__main__":
    fix_image_paths()
