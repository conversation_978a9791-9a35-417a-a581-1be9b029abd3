#!/usr/bin/env python3
"""
Genera report HTML qualitativo per i modelli baseline
con esempi, ground truth e predizioni
"""

import json
import os
import base64
import random
from datetime import datetime
import argparse

def load_dataset_mapping():
    """Carica la mappatura tra example_id e image_path dal dataset con colori corretti"""
    dataset_file = "/work/tesi_ediluzio/data/processed/xml_format_optimized/baseline_t7_corrected_400_colors_fixed.json"
    try:
        with open(dataset_file, 'r') as f:
            dataset = json.load(f)

        mapping = {}
        for i, example in enumerate(dataset):
            # Usa le nuove immagini con colori corretti
            image_path = f"/work/tesi_ediluzio/data/processed/xml_format_optimized/baseline_t7_images_colors_fixed/{example.get('id', f'example_{i}')}.png"
            mapping[i] = {
                'svg_content': example.get('xml', ''),
                'ground_truth': example.get('caption', ''),
                'image_path': image_path if os.path.exists(image_path) else ''
            }

        print(f"📊 Caricata mappatura per {len(mapping)} esempi")
        return mapping
    except Exception as e:
        print(f"❌ Errore caricamento dataset: {e}")
        return {}

def image_to_base64(image_path):
    """Converte immagine in base64 per embedding HTML"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        return base64.b64encode(image_data).decode('utf-8')
    except Exception as e:
        print(f"⚠️ Errore conversione immagine {image_path}: {e}")
        return None

def load_model_results(results_file):
    """Carica risultati di un modello"""
    try:
        with open(results_file, 'r') as f:
            data = json.load(f)
        
        # Gestisci diversi formati di file
        if isinstance(data, list):
            return data
        elif isinstance(data, dict) and 'results' in data:
            return data['results']
        elif isinstance(data, dict) and 'predictions' in data:
            return data['predictions']
        else:
            print(f"❌ Formato file non riconosciuto: {results_file}")
            return []
    except Exception as e:
        print(f"❌ Errore caricamento {results_file}: {e}")
        return []

def select_examples(model_results, dataset_mapping, num_examples=10):
    """Seleziona esempi rappresentativi per il report"""
    valid_examples = []

    for result in model_results:
        if result.get('success', False) and 'example_id' in result:
            example_id = result['example_id']
            if example_id in dataset_mapping:
                mapping_data = dataset_mapping[example_id]
                if mapping_data['image_path'] and os.path.exists(mapping_data['image_path']):  # Usa PNG corrette
                    valid_examples.append({
                        'example_id': example_id,
                        'prediction': result['prediction'],
                        'ground_truth': mapping_data['ground_truth'],
                        'image_path': mapping_data['image_path']
                    })

    # Seleziona esempi casuali
    if len(valid_examples) > num_examples:
        return random.sample(valid_examples, num_examples)
    return valid_examples

def generate_html_report(models_data, output_file, num_examples=10):
    """Genera report HTML qualitativo"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Baseline Models - Qualitative Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }}
        .header {{
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }}
        .model {{ 
            margin: 30px 0; 
            border: 2px solid #ddd; 
            padding: 25px; 
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .model h2 {{
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        .example {{ 
            display: flex; 
            margin: 25px 0; 
            border-bottom: 1px solid #eee; 
            padding: 20px 0; 
            align-items: flex-start;
        }}
        .image {{ 
            flex: 0 0 250px; 
            margin-right: 25px; 
            text-align: center;
        }}
        .text {{ 
            flex: 1; 
        }}
        .gt {{ 
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }}
        .pred {{ 
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }}
        .pred.blip2 {{ border-left-color: #ff6b6b; }}
        .pred.florence2 {{ border-left-color: #4ecdc4; }}
        .pred.idefics3 {{ border-left-color: #45b7d1; }}
        
        img {{
            max-width: 220px;
            max-height: 220px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }}
        .example-header {{
            font-weight: bold;
            color: #666;
            margin-bottom: 10px;
            font-size: 14px;
        }}
        .stats {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            color: #666;
            border-top: 1px solid #eee;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Baseline Models - Qualitative Report</h1>
        <p>Generated: {timestamp}</p>
        <p>Showing {num_examples} representative examples per model</p>
    </div>
"""

    for model_name, examples in models_data.items():
        if not examples:
            continue
            
        html_content += f"""
    <div class="model">
        <h2>{model_name}</h2>
        <div class="stats">
            <strong>📊 Statistics:</strong> {len(examples)} examples shown
        </div>
"""

        for i, example in enumerate(examples):
            image_b64 = image_to_base64(example['image_path'])
            if not image_b64:
                continue

            model_class = model_name.lower().replace('-', '').replace('_', '')

            html_content += f"""
        <div class="example">
            <div class="image">
                <div class="example-header">Example {example['example_id']}</div>
                <img src="data:image/png;base64,{image_b64}" alt="Example {example['example_id']}">
            </div>
            <div class="text">
                <div class="gt">
                    <strong>🎯 Ground Truth:</strong><br>
                    {example['ground_truth']}
                </div>
                <div class="pred {model_class}">
                    <strong>🤖 {model_name} Prediction:</strong><br>
                    {example['prediction']}
                </div>
            </div>
        </div>
"""

        html_content += "    </div>\n"

    html_content += f"""
    <div class="footer">
        <p>📝 Report generated automatically from baseline model results</p>
        <p>🕒 Timestamp: {timestamp}</p>
        <p>🔬 Models evaluated: {', '.join(models_data.keys())}</p>
    </div>
</body>
</html>"""

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Report HTML generato: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Genera report HTML qualitativo per modelli baseline')
    parser.add_argument('--output', default='evaluation_results/baseline_qualitative_report.html', 
                       help='File HTML di output')
    parser.add_argument('--examples', type=int, default=10, 
                       help='Numero di esempi per modello (default: 10)')
    parser.add_argument('--models', nargs='+', 
                       default=['BLIP-2', 'Florence2', 'IDEFICS3'],
                       help='Modelli da includere nel report')
    
    args = parser.parse_args()
    
    print("🚀 Generazione report qualitativo baseline...")
    
    # Carica mappatura dataset
    dataset_mapping = load_dataset_mapping()
    if not dataset_mapping:
        print("❌ Impossibile caricare dataset mapping")
        return
    
    # File risultati modelli
    model_files = {
        'BLIP-2': 'evaluation_results/baseline_optimized/blip2_results.json',
        'Florence2': 'evaluation_results/baseline_optimized/florence2_results.json', 
        'IDEFICS3': 'evaluation_results/baseline_optimized/idefics3_results.json'
    }
    
    # Carica risultati per ogni modello
    models_data = {}
    for model_name in args.models:
        if model_name in model_files:
            print(f"📥 Caricamento risultati {model_name}...")
            results = load_model_results(model_files[model_name])
            if results:
                examples = select_examples(results, dataset_mapping, args.examples)
                models_data[model_name] = examples
                print(f"✅ {model_name}: {len(examples)} esempi selezionati")
            else:
                print(f"⚠️ Nessun risultato trovato per {model_name}")
        else:
            print(f"⚠️ Modello {model_name} non riconosciuto")
    
    if not models_data:
        print("❌ Nessun dato da processare")
        return
    
    # Genera report HTML
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    generate_html_report(models_data, args.output, args.examples)
    
    print(f"\n🎉 Report completato!")
    print(f"📁 File: {args.output}")
    print(f"📊 Modelli: {len(models_data)}")
    print(f"📝 Esempi totali: {sum(len(examples) for examples in models_data.values())}")

if __name__ == "__main__":
    main()
