#!/usr/bin/env python3
"""
Script per lanciare inferenza baseline su tutti i modelli con immagini corrette
"""

import subprocess
import os
import logging
import time
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_model_inference(model_name, max_examples=100):
    """Lancia inferenza per un modello specifico"""
    
    output_dir = f"evaluation_results/baseline_colors_fixed/{model_name}"
    
    logger.info(f"🚀 Avvio inferenza {model_name}...")
    logger.info(f"   Output: {output_dir}")
    logger.info(f"   Esempi: {max_examples}")
    
    # Crea directory output
    os.makedirs(output_dir, exist_ok=True)
    
    # Comando
    cmd = [
        "python", "scripts/evaluation/baseline_inference_hf_official.py",
        "--model", model_name,
        "--test_file", "data/processed/xml_format_optimized/baseline_t7_corrected_400_colors_fixed.json",
        "--output_dir", output_dir,
        "--max_examples", str(max_examples)
    ]
    
    logger.info(f"🔧 Comando: {' '.join(cmd)}")
    
    # Lancia processo
    start_time = time.time()
    
    try:
        result = subprocess.run(
            cmd,
            cwd="/work/tesi_ediluzio",
            capture_output=True,
            text=True,
            timeout=3600  # 1 ora timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            logger.info(f"✅ {model_name} completato in {duration:.1f}s")
            logger.info(f"📁 Risultati salvati in: {output_dir}")
            
            # Log output
            if result.stdout:
                logger.info(f"📝 Output {model_name}:")
                for line in result.stdout.strip().split('\n')[-10:]:  # Ultime 10 righe
                    logger.info(f"   {line}")
            
            return True
        else:
            logger.error(f"❌ {model_name} fallito (exit code: {result.returncode})")
            logger.error(f"📝 Stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"⏰ {model_name} timeout dopo 1 ora")
        return False
    except Exception as e:
        logger.error(f"❌ {model_name} errore: {e}")
        return False

def main():
    logger.info("🎯 BASELINE INFERENCE - IMMAGINI COLORI CORRETTI")
    logger.info("=" * 60)
    logger.info(f"🕒 Inizio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Modelli da testare
    models = ["blip2", "florence2", "idefics3"]
    max_examples = 100
    
    logger.info(f"🤖 Modelli: {', '.join(models)}")
    logger.info(f"📊 Esempi per modello: {max_examples}")
    logger.info(f"🖼️ Dataset: baseline_t7_corrected_400_colors_fixed.json")
    logger.info(f"🎨 Immagini: baseline_t7_images_colors_fixed/")
    
    # Verifica che il dataset esista
    dataset_file = "data/processed/xml_format_optimized/baseline_t7_corrected_400_colors_fixed.json"
    if not os.path.exists(dataset_file):
        logger.error(f"❌ Dataset non trovato: {dataset_file}")
        return
    
    # Verifica che le immagini esistano
    images_dir = "data/processed/xml_format_optimized/baseline_t7_images_colors_fixed"
    if not os.path.exists(images_dir):
        logger.error(f"❌ Directory immagini non trovata: {images_dir}")
        return
    
    # Conta immagini disponibili
    import glob
    image_files = glob.glob(f"{images_dir}/*.png")
    logger.info(f"🖼️ Immagini disponibili: {len(image_files)}")
    
    if len(image_files) == 0:
        logger.error("❌ Nessuna immagine PNG trovata!")
        return
    
    # Lancia inferenza per ogni modello
    results = {}
    total_start_time = time.time()
    
    for model_name in models:
        logger.info(f"\n{'='*40}")
        logger.info(f"🤖 MODELLO: {model_name.upper()}")
        logger.info(f"{'='*40}")
        
        success = run_model_inference(model_name, max_examples)
        results[model_name] = success
        
        if success:
            logger.info(f"✅ {model_name} completato con successo")
        else:
            logger.error(f"❌ {model_name} fallito")
        
        # Pausa tra modelli per evitare problemi di memoria
        if model_name != models[-1]:  # Non fare pausa dopo l'ultimo modello
            logger.info("⏸️ Pausa 30s tra modelli...")
            time.sleep(30)
    
    # Riassunto finale
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    logger.info(f"\n{'='*60}")
    logger.info("🎉 RIASSUNTO FINALE")
    logger.info(f"{'='*60}")
    logger.info(f"🕒 Durata totale: {total_duration/60:.1f} minuti")
    
    successful_models = [model for model, success in results.items() if success]
    failed_models = [model for model, success in results.items() if not success]
    
    logger.info(f"✅ Modelli completati: {len(successful_models)}/{len(models)}")
    if successful_models:
        logger.info(f"   Successi: {', '.join(successful_models)}")
    
    if failed_models:
        logger.info(f"❌ Modelli falliti: {', '.join(failed_models)}")
    
    # Verifica file di output
    logger.info(f"\n📁 FILE DI OUTPUT:")
    for model_name in models:
        output_dir = f"evaluation_results/baseline_colors_fixed/{model_name}"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            logger.info(f"   {model_name}: {len(files)} file in {output_dir}")
        else:
            logger.info(f"   {model_name}: ❌ Directory non creata")
    
    logger.info(f"\n🏁 Processo completato: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if len(successful_models) == len(models):
        logger.info("🎉 TUTTI I MODELLI COMPLETATI CON SUCCESSO!")
        logger.info("🔄 Prossimo step: Calcolare metriche con le nuove predizioni")
    else:
        logger.warning(f"⚠️ Solo {len(successful_models)}/{len(models)} modelli completati")

if __name__ == "__main__":
    main()
