#!/usr/bin/env python3
"""
Test CLIPScore implementation
"""

import sys
import os
import logging
import torch
from PIL import Image
from transformers import CLIPProcessor, CLIPModel

sys.path.append('/work/tesi_ediluzio')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_clip_score():
    """Test CLIPScore con un esempio reale usando la formula corretta"""

    # Esempio di test
    image_path = "/work/tesi_ediluzio/data/processed/xml_format_optimized/baseline_t7_images_full/unknown_sr_051299_dup_4552.png"
    text = "The image depicts a simple, black and white illustration of an ambulance"

    if not os.path.exists(image_path):
        logger.error(f"❌ Immagine non trovata: {image_path}")
        return

    logger.info(f"🧪 Test CLIPScore (formula corretta)...")
    logger.info(f"   Immagine: {os.path.basename(image_path)}")
    logger.info(f"   Testo: {text}")

    try:
        # Carica CLIP direttamente
        logger.info("📥 Caricamento CLIP model...")
        model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
        processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")

        # Carica immagine
        image = Image.open(image_path).convert('RGB')

        # Processa input separatamente
        text_inputs = processor(text=[text], return_tensors="pt", padding=True, truncation=True, max_length=77)
        image_inputs = processor(images=[image], return_tensors="pt")

        # Calcola embeddings e CLIPScore corretto
        with torch.no_grad():
            # Ottieni embeddings normalizzati
            text_features = model.get_text_features(**text_inputs)
            image_features = model.get_image_features(**image_inputs)

            # Normalizza gli embeddings
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)

            # Calcola similarità coseno
            cosine_similarity = torch.sum(text_features * image_features, dim=-1)

            # CLIPScore = max(100 * cosine_similarity, 0)
            clip_score = torch.clamp(100 * cosine_similarity, min=0.0)

            logger.info(f"✅ Cosine Similarity: {cosine_similarity.item():.4f}")
            logger.info(f"✅ CLIPScore (corretto): {clip_score.item():.4f}")

    except Exception as e:
        logger.error(f"❌ Errore test CLIPScore: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_clip_score()
