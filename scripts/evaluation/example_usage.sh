#!/bin/bash
# Esempi di utilizzo dello script evaluate_any_model.py

echo "🚀 ESEMPI DI UTILIZZO - evaluate_any_model.py"
echo "=============================================="

echo ""
echo "1️⃣ VALUTARE UN SINGOLO MODELLO:"
echo "python scripts/evaluation/evaluate_any_model.py \\"
echo "    --results_file evaluation_results/my_model/my_model_results.json \\"
echo "    --model_name \"MyAwesomeModel\" \\"
echo "    --output_dir evaluation_results/my_model/"

echo ""
echo "2️⃣ VALUTARE E CONFRONTARE CON BASELINE:"
echo "python scripts/evaluation/evaluate_any_model.py \\"
echo "    --results_file evaluation_results/my_model/my_model_results.json \\"
echo "    --model_name \"MyAwesomeModel\" \\"
echo "    --output_dir evaluation_results/comparison/ \\"
echo "    --compare_with evaluation_results/baseline_optimized/baseline_metrics_complete.json"

echo ""
echo "3️⃣ VALUTARE MODELLO GEMMA (esempio):"
echo "python scripts/evaluation/evaluate_any_model.py \\"
echo "    --results_file evaluation_results/gemma/gemma_results.json \\"
echo "    --model_name \"Gemma-2B-SVG\" \\"
echo "    --output_dir evaluation_results/gemma/ \\"
echo "    --compare_with evaluation_results/baseline_optimized/baseline_metrics_complete.json"

echo ""
echo "4️⃣ CONFRONTARE PIÙ MODELLI:"
echo "# Prima valuta il primo modello"
echo "python scripts/evaluation/evaluate_any_model.py \\"
echo "    --results_file evaluation_results/model1/results.json \\"
echo "    --model_name \"Model1\" \\"
echo "    --output_dir evaluation_results/comparison/"
echo ""
echo "# Poi aggiungi il secondo modello al confronto"
echo "python scripts/evaluation/evaluate_any_model.py \\"
echo "    --results_file evaluation_results/model2/results.json \\"
echo "    --model_name \"Model2\" \\"
echo "    --output_dir evaluation_results/comparison/ \\"
echo "    --compare_with evaluation_results/comparison/Model1_metrics_*.json"

echo ""
echo "📋 FORMATO FILE RISULTATI RICHIESTO:"
echo "Il file JSON deve contenere una lista di oggetti con questa struttura:"
echo "["
echo "  {"
echo "    \"example_id\": 0,"
echo "    \"prediction\": \"Generated caption here\","
echo "    \"ground_truth\": \"Reference caption here\","
echo "    \"success\": true"
echo "  },"
echo "  ..."
echo "]"

echo ""
echo "📊 OUTPUT GENERATO:"
echo "- File JSON con tutte le metriche"
echo "- Grafico radar PNG e PDF"
echo "- Tabella con valori dettagliati"
echo "- Log completo della valutazione"

echo ""
echo "🎯 METRICHE CALCOLATE:"
echo "- BLEU-1, BLEU-2, BLEU-3, BLEU-4"
echo "- METEOR"
echo "- CIDEr"
echo "- CLIPScore (corretto!)"
echo "- ROUGE-1, ROUGE-2, ROUGE-L"
echo "- Diversità predizioni"
echo "- Success rate"

echo ""
echo "✅ PRONTO PER L'USO!"
