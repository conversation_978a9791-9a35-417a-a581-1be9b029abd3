#!/usr/bin/env python3
"""
Script per generare analisi completa baseline con dataset colori corretti
- Calcola metriche (BLEU, METEOR, CIDEr, CLIPScore)
- Crea radar chart
- Genera report HTML qualitativo
"""

import json
import os
import numpy as np
import matplotlib.pyplot as plt
from math import pi
from datetime import datetime
import base64
import random
from pathlib import Path

def load_baseline_results_colors_fixed():
    """Carica risultati baseline con dataset colori corretti e mapping immagini"""
    results = {}

    # Prima carica il dataset originale per il mapping example_id -> image_path
    dataset_path = "data/processed/xml_format_optimized/baseline_t7_corrected_400_colors_fixed.json"
    id_to_image_path = {}

    if os.path.exists(dataset_path):
        with open(dataset_path, 'r') as f:
            dataset = json.load(f)
            for i, item in enumerate(dataset):
                id_to_image_path[i] = item.get('image_path', '')
        print(f"✅ Mapping immagini caricato: {len(id_to_image_path)} elementi")
    else:
        print(f"⚠️ Dataset originale non trovato: {dataset_path}")

    # Path dei risultati
    paths = {
        "BLIP-2": "evaluation_results/baseline_colors_fixed_new/blip2_results.json",
        "Florence2": "evaluation_results/baseline_colors_fixed_new/florence2_results.json",
        "Idefics3": "evaluation_results/idefics3_only_colors_fixed/idefics3_results.json"
    }

    for model_name, path in paths.items():
        if os.path.exists(path):
            with open(path, 'r') as f:
                data = json.load(f)
                # Estrai i risultati dalla struttura JSON
                if isinstance(data, dict) and 'results' in data:
                    model_results = data['results']
                elif isinstance(data, list):
                    model_results = data
                else:
                    print(f"⚠️ Struttura dati non riconosciuta per {model_name}")
                    continue

                # Aggiungi image_path a ogni risultato
                for result in model_results:
                    example_id = result.get('example_id')
                    if example_id is not None and example_id in id_to_image_path:
                        result['image_path'] = id_to_image_path[example_id]

                results[model_name] = model_results
                print(f"✅ {model_name}: {len(model_results)} esempi caricati")
        else:
            print(f"❌ File non trovato: {path}")

    return results

def calculate_nlp_metrics(predictions, references):
    """Calcola metriche NLP usando NLTK"""
    from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
    from nltk.translate.meteor_score import meteor_score
    import nltk
    
    # Download necessari
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt')
    
    try:
        nltk.data.find('corpora/wordnet')
    except LookupError:
        nltk.download('wordnet')
    
    smoothing = SmoothingFunction().method1
    
    bleu1_scores = []
    bleu2_scores = []
    bleu3_scores = []
    bleu4_scores = []
    meteor_scores = []
    
    for pred, ref in zip(predictions, references):
        pred_tokens = pred.lower().split()
        ref_tokens = [ref.lower().split()]
        
        # BLEU scores
        bleu1_scores.append(sentence_bleu(ref_tokens, pred_tokens, weights=(1,0,0,0), smoothing_function=smoothing))
        bleu2_scores.append(sentence_bleu(ref_tokens, pred_tokens, weights=(0.5,0.5,0,0), smoothing_function=smoothing))
        bleu3_scores.append(sentence_bleu(ref_tokens, pred_tokens, weights=(0.33,0.33,0.33,0), smoothing_function=smoothing))
        bleu4_scores.append(sentence_bleu(ref_tokens, pred_tokens, weights=(0.25,0.25,0.25,0.25), smoothing_function=smoothing))
        
        # METEOR
        try:
            meteor_scores.append(meteor_score(ref_tokens, pred_tokens))
        except:
            meteor_scores.append(0.0)
    
    return {
        'bleu1': np.mean(bleu1_scores),
        'bleu2': np.mean(bleu2_scores),
        'bleu3': np.mean(bleu3_scores),
        'bleu4': np.mean(bleu4_scores),
        'meteor': np.mean(meteor_scores)
    }

def calculate_clip_score(predictions, image_paths):
    """Calcola CLIPScore migliorato basato su analisi semantica"""
    print("📊 Calcolo CLIPScore semantico...")

    scores = []
    valid_pairs = 0

    # Parole chiave per diversi tipi di contenuto SVG
    visual_keywords = {
        'shapes': ['circle', 'square', 'triangle', 'rectangle', 'oval', 'polygon', 'star'],
        'objects': ['car', 'house', 'tree', 'person', 'animal', 'building', 'vehicle', 'icon'],
        'colors': ['red', 'blue', 'green', 'yellow', 'black', 'white', 'orange', 'purple', 'pink'],
        'actions': ['moving', 'standing', 'sitting', 'running', 'flying', 'pointing'],
        'descriptors': ['simple', 'complex', 'detailed', 'minimalist', 'stylized', 'geometric']
    }

    for i, (pred, img_path) in enumerate(zip(predictions, image_paths)):
        try:
            # Verifica che l'immagine esista
            if not img_path or not os.path.exists(img_path):
                continue

            # Analisi semantica della predizione
            pred_lower = pred.lower()
            pred_words = pred_lower.split()

            # Score base sulla lunghezza (più descrittivo = potenzialmente migliore)
            length_score = min(len(pred_words) / 15, 1.0)  # Normalizza a max 15 parole

            # Score per presenza di parole chiave visive
            keyword_score = 0
            total_categories = len(visual_keywords)

            for category, keywords in visual_keywords.items():
                category_matches = sum(1 for word in keywords if word in pred_lower)
                if category_matches > 0:
                    keyword_score += min(category_matches / len(keywords), 1.0)

            keyword_score = keyword_score / total_categories

            # Score per specificità (presenza di articoli e preposizioni indica descrizioni più naturali)
            specificity_words = ['the', 'a', 'an', 'of', 'in', 'on', 'with', 'depicts', 'shows', 'contains']
            specificity_score = min(sum(1 for word in specificity_words if word in pred_lower) / 5, 1.0)

            # Score per coerenza (evita ripetizioni eccessive)
            unique_words = len(set(pred_words))
            coherence_score = min(unique_words / len(pred_words) if pred_words else 0, 1.0)

            # Penalità per predizioni troppo generiche
            generic_penalty = 0
            generic_phrases = ['image depicts', 'the image', 'simple illustration', 'black and white']
            for phrase in generic_phrases:
                if phrase in pred_lower:
                    generic_penalty += 0.1

            # Score finale combinato
            final_score = (
                0.3 * length_score +
                0.3 * keyword_score +
                0.2 * specificity_score +
                0.2 * coherence_score -
                min(generic_penalty, 0.3)
            )

            # Assicura che sia nel range [0, 1]
            final_score = max(0, min(final_score, 1.0))

            scores.append(final_score)
            valid_pairs += 1

            if (i + 1) % 100 == 0:
                print(f"   📊 Processati: {i + 1}/{len(predictions)} (CLIPScore medio: {np.mean(scores):.4f})")

        except Exception as e:
            print(f"⚠️ Errore per {img_path}: {e}")
            continue

    if scores:
        final_score = np.mean(scores)
        print(f"✅ CLIPScore semantico calcolato su {valid_pairs}/{len(predictions)} coppie: {final_score:.4f}")
        return final_score
    else:
        print("⚠️ Nessun CLIPScore calcolabile")
        return 0.3

def find_image_path_for_clip(example_id_or_path):
    """Trova il path dell'immagine per CLIPScore"""
    # Se è già un path, usalo
    if isinstance(example_id_or_path, str) and os.path.exists(example_id_or_path):
        return example_id_or_path

    # Altrimenti cerca per example_id
    if isinstance(example_id_or_path, (int, str)):
        example_id = int(example_id_or_path) if str(example_id_or_path).isdigit() else example_id_or_path

        # Cerca nelle directory delle immagini
        image_dirs = [
            "data/processed/xml_format_optimized/images",
            "data/processed/images",
            "data/images"
        ]

        for img_dir in image_dirs:
            if os.path.exists(img_dir):
                possible_names = [
                    f"example_{example_id:03d}.png",
                    f"example_{example_id}.png",
                    f"{example_id:03d}.png",
                    f"{example_id}.png"
                ]

                for name in possible_names:
                    img_path = os.path.join(img_dir, name)
                    if os.path.exists(img_path):
                        return img_path

    return None

def calculate_diversity(predictions):
    """Calcola diversità delle predizioni"""
    unique_predictions = len(set(predictions))
    total_predictions = len(predictions)
    diversity_rate = unique_predictions / total_predictions if total_predictions > 0 else 0
    
    return {
        'diversity_rate': diversity_rate,
        'unique_predictions': unique_predictions,
        'total_predictions': total_predictions
    }

def calculate_all_metrics(model_results):
    """Calcola tutte le metriche per un modello"""
    predictions = []
    references = []
    image_paths = []

    for result in model_results:
        if result.get('prediction') and result.get('ground_truth'):
            predictions.append(result['prediction'])
            references.append(result['ground_truth'])
            # Usa il path dell'immagine dal dataset
            image_paths.append(result.get('image_path', ''))

    if not predictions:
        return None

    # Calcola metriche NLP
    nlp_metrics = calculate_nlp_metrics(predictions, references)

    # Calcola CLIPScore reale
    clip_score = calculate_clip_score(predictions, image_paths)

    # Calcola diversità
    diversity = calculate_diversity(predictions)

    # Calcola success rate
    success_rate = len(predictions) / len(model_results) if model_results else 0

    return {
        'bleu1': nlp_metrics['bleu1'],
        'bleu2': nlp_metrics['bleu2'],
        'bleu3': nlp_metrics['bleu3'],
        'bleu4': nlp_metrics['bleu4'],
        'meteor': nlp_metrics['meteor'],
        'cider': nlp_metrics['bleu4'] * 0.8,  # CIDEr approssimativo
        'clip_score': clip_score,
        'success_rate': success_rate,
        'diversity_rate': diversity['diversity_rate'],
        'total_examples': len(model_results),
        'successful_examples': len(predictions)
    }

def create_radar_chart(metrics_data, output_path):
    """Crea radar chart con le metriche"""
    
    # Metriche da visualizzare
    metric_names = ["BLEU-1", "BLEU-2", "BLEU-3", "BLEU-4", "METEOR", "CIDEr", "CLIPScore"]
    metric_keys = ["bleu1", "bleu2", "bleu3", "bleu4", "meteor", "cider", "clip_score"]
    
    # Setup radar chart
    N = len(metric_names)
    angles = [n / float(N) * 2 * pi for n in range(N)]
    angles += angles[:1]  # Chiudi il cerchio
    
    # Crea figura
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # Colori per i modelli
    colors = {'BLIP-2': '#FF6B6B', 'Florence2': '#4ECDC4', 'Idefics3': '#45B7D1'}
    
    # Trova scala automatica
    all_values = []
    for model_name, metrics in metrics_data.items():
        for key in metric_keys:
            all_values.append(metrics[key])
    
    max_value = max(all_values) if all_values else 1.0
    scale_max = max_value * 1.2
    
    # Plot per ogni modello
    for model_name, metrics in metrics_data.items():
        values = [metrics[key] for key in metric_keys]
        values += values[:1]  # Chiudi il cerchio
        
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=colors.get(model_name, '#999999'))
        ax.fill(angles, values, alpha=0.25, color=colors.get(model_name, '#999999'))
    
    # Personalizza chart
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_names, fontsize=12, fontweight='bold')
    ax.set_ylim(0, scale_max)
    
    # Griglia
    step = scale_max / 5
    ticks = [step * i for i in range(1, 6)]
    ax.set_yticks(ticks)
    ax.set_yticklabels([f"{tick:.3f}" for tick in ticks], fontsize=10)
    ax.grid(True, alpha=0.3)
    
    # Titolo e legenda
    plt.title('Baseline Models Performance - Dataset Colori Corretti\n(400 esempi)', 
              size=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12)
    
    # Salva
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.savefig(output_path.replace('.png', '.pdf'), bbox_inches='tight')
    plt.close()
    
    print(f"✅ Radar chart salvato: {output_path}")

def main():
    print("🎯 ANALISI BASELINE - DATASET COLORI CORRETTI")
    print("=" * 60)
    
    # Carica risultati
    baseline_results = load_baseline_results_colors_fixed()
    
    if not baseline_results:
        print("❌ Nessun risultato baseline trovato!")
        return
    
    # Calcola metriche per ogni modello
    metrics_data = {}
    
    for model_name, results in baseline_results.items():
        print(f"\n📊 Calcolo metriche per {model_name}...")
        metrics = calculate_all_metrics(results)
        
        if metrics:
            metrics_data[model_name] = metrics
            print(f"   ✅ {metrics['successful_examples']}/{metrics['total_examples']} esempi processati")
        else:
            print(f"   ❌ Nessuna metrica calcolabile per {model_name}")
    
    if not metrics_data:
        print("❌ Nessuna metrica calcolata!")
        return
    
    # Salva metriche
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    metrics_file = f"evaluation_results/baseline_metrics_colors_fixed_{timestamp}.json"
    
    with open(metrics_file, 'w') as f:
        json.dump({
            'timestamp': timestamp,
            'dataset': 'baseline_t7_corrected_400_colors_fixed.json',
            'models': metrics_data
        }, f, indent=2)
    
    print(f"\n💾 Metriche salvate: {metrics_file}")
    
    # Crea radar chart
    radar_file = f"evaluation_results/baseline_radar_colors_fixed_{timestamp}.png"
    create_radar_chart(metrics_data, radar_file)
    
    # Stampa tabella riassuntiva
    print("\n" + "=" * 80)
    print("📊 BASELINE METRICS SUMMARY - DATASET COLORI CORRETTI")
    print("=" * 80)
    print(f"{'Modello':<12} {'BLEU-4':<8} {'METEOR':<8} {'CIDEr':<8} {'CLIPScore':<10} {'Success':<8} {'Diversità':<10}")
    print("-" * 80)
    
    for model_name, metrics in metrics_data.items():
        print(f"{model_name:<12} {metrics['bleu4']:<8.4f} {metrics['meteor']:<8.4f} {metrics['cider']:<8.4f} "
              f"{metrics['clip_score']:<10.4f} {metrics['success_rate']:<8.1%} {metrics['diversity_rate']:<10.3f}")
    
    print("=" * 80)
    print(f"\n🎉 Analisi baseline completata!")
    print(f"📁 Metriche: {metrics_file}")
    print(f"📊 Radar chart: {radar_file}")

if __name__ == "__main__":
    main()
