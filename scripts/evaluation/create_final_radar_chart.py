#!/usr/bin/env python3
"""
Crea grafico radar finale con tutte le metriche baseline
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from math import pi
import os

def create_radar_chart(metrics_data, output_path):
    """Crea grafico radar con le metriche baseline"""

    # Metriche da visualizzare (come nell'immagine)
    metric_names = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIPScore']

    # Estrai dati per ogni modello e normalizza CLIPScore (0-100 -> 0-1)
    models_data = {}
    models_raw_data = {}
    for model_name, model_metrics in metrics_data.items():
        raw_values = [
            model_metrics['bleu1'],
            model_metrics['bleu2'],
            model_metrics['bleu3'],
            model_metrics['bleu4'],
            model_metrics['meteor'],
            model_metrics['cider'],
            model_metrics['clip_score']
        ]

        # Normalizza CLIPScore da 0-100 a 0-1 per il grafico
        normalized_values = raw_values.copy()
        normalized_values[6] = raw_values[6] / 100.0  # CLIPScore

        models_data[model_name] = normalized_values
        models_raw_data[model_name] = raw_values
    
    # Setup del grafico
    fig, ax = plt.subplots(figsize=(16, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = [n / float(len(metric_names)) * 2 * pi for n in range(len(metric_names))]
    angles += angles[:1]  # Chiudi il cerchio
    
    # Colori per ogni modello
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    # Plotta ogni modello con valori nella legenda
    legend_labels = []
    for i, (model_name, values) in enumerate(models_data.items()):
        values += values[:1]  # Chiudi il cerchio
        raw_values = models_raw_data[model_name]

        # Crea label con valori principali
        label_text = f"{model_name}\n(BLEU-4: {raw_values[3]:.3f}, METEOR: {raw_values[4]:.3f}, CLIPScore: {raw_values[6]:.1f})"
        legend_labels.append(label_text)

        ax.plot(angles, values, 'o-', linewidth=2, color=colors[i])
        ax.fill(angles, values, alpha=0.25, color=colors[i])
    
    # Personalizza il grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_names, fontsize=12, fontweight='bold')
    
    # Imposta i limiti radiali
    ax.set_ylim(0, 1.0)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=10)
    ax.grid(True)
    
    # Titolo e legenda con valori
    plt.title('Baseline Models Performance - SVG Captioning\n(100 Examples)',
              size=16, fontweight='bold', pad=20)

    plt.legend(legend_labels, loc='upper right', bbox_to_anchor=(1.5, 1.0), fontsize=10)
    
    # Aggiungi tabella con valori sulla sinistra
    table_data = []
    table_data.append(['Metric'] + list(models_raw_data.keys()))

    for i, metric in enumerate(metric_names):
        row = [metric]
        for model_name in models_raw_data.keys():
            value = models_raw_data[model_name][i]
            if metric == 'CLIPScore':
                row.append(f'{value:.1f}')
            else:
                row.append(f'{value:.4f}')
        table_data.append(row)

    # Posiziona tabella
    table_text = ""
    for row in table_data:
        table_text += f"{'  '.join(f'{cell:<12}' for cell in row)}\n"

    plt.figtext(0.02, 0.5, table_text, fontsize=9, fontfamily='monospace',
                verticalalignment='center', bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

    # Aggiungi note
    note_text = "BLEU-1,2,3,4, METEOR, CIDEr: Higher is better | CLIPScore: Image-text similarity (0-100)"
    plt.figtext(0.5, 0.02, note_text, ha='center', fontsize=10, style='italic')
    
    # Salva
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.savefig(output_path.replace('.png', '.pdf'), dpi=300, bbox_inches='tight')
    
    print(f"✅ Grafico radar salvato: {output_path}")
    print(f"✅ Grafico radar PDF: {output_path.replace('.png', '.pdf')}")

def print_metrics_table(metrics_data):
    """Stampa tabella riassuntiva delle metriche"""
    print("\n" + "="*100)
    print("📊 BASELINE MODELS PERFORMANCE SUMMARY")
    print("="*100)
    print(f"{'Model':<12} {'BLEU-1':<8} {'BLEU-2':<8} {'BLEU-3':<8} {'BLEU-4':<8} {'METEOR':<8} {'CIDEr':<8} {'CLIPScore':<10}")
    print("-"*100)
    
    for model_name, metrics in metrics_data.items():
        print(f"{model_name:<12} {metrics['bleu1']:<8.4f} {metrics['bleu2']:<8.4f} "
              f"{metrics['bleu3']:<8.4f} {metrics['bleu4']:<8.4f} {metrics['meteor']:<8.4f} "
              f"{metrics['cider']:<8.4f} {metrics['clip_score']:<10.4f}")
    
    print("="*100)

def main():
    # Carica risultati
    metrics_file = "/work/tesi_ediluzio/evaluation_results/baseline_optimized/baseline_metrics_complete.json"
    
    with open(metrics_file, 'r') as f:
        data = json.load(f)
    
    metrics_data = data['metrics']
    
    # Stampa tabella
    print_metrics_table(metrics_data)
    
    # Crea grafico radar
    output_dir = "/work/tesi_ediluzio/evaluation_results"
    output_path = os.path.join(output_dir, "baseline_radar_chart_final_complete.png")
    
    create_radar_chart(metrics_data, output_path)
    
    print(f"\n🎉 Grafico radar finale completato!")
    print(f"📁 File salvato in: {output_path}")

if __name__ == "__main__":
    main()
