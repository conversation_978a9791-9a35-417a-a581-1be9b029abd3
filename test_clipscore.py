#!/usr/bin/env python3
"""
Test script per verificare CLIPScore
"""

import json
import os
import torch
from PIL import Image
from transformers import AutoModel, AutoProcessor
import numpy as np

def test_clipscore():
    print("🧪 TEST CLIPSCORE")
    print("=" * 50)
    
    # Carica un esempio dal dataset
    dataset_path = "data/processed/xml_format_optimized/baseline_t7_corrected_400_colors_fixed.json"
    results_path = "evaluation_results/baseline_colors_fixed_new/blip2_results.json"
    
    if not os.path.exists(dataset_path) or not os.path.exists(results_path):
        print("❌ File non trovati")
        return
    
    # Carica dataset e risultati
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    with open(results_path, 'r') as f:
        results = json.load(f)['results']
    
    # Prendi primi 3 esempi
    test_examples = results[:3]
    
    print(f"📊 Test su {len(test_examples)} esempi")
    
    # Carica modello CLIP
    print("🔄 Caricamento CLIP...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    model = AutoModel.from_pretrained("openai/clip-vit-base-patch32").to(device)
    processor = AutoProcessor.from_pretrained("openai/clip-vit-base-patch32")
    
    scores = []
    
    with torch.no_grad():
        for i, example in enumerate(test_examples):
            example_id = example['example_id']
            prediction = example['prediction']
            
            # Trova immagine
            if example_id < len(dataset):
                img_path = dataset[example_id]['image_path']
                
                print(f"\n📝 Esempio {i+1}:")
                print(f"   ID: {example_id}")
                print(f"   Predizione: {prediction}")
                print(f"   Immagine: {img_path}")
                
                if os.path.exists(img_path):
                    try:
                        # Carica immagine
                        image = Image.open(img_path).convert('RGB')
                        print(f"   ✅ Immagine caricata: {image.size}")
                        
                        # Processa
                        inputs = processor(text=prediction, images=image, return_tensors="pt", padding=True)
                        
                        # Sposta su device
                        for key in inputs:
                            inputs[key] = inputs[key].to(device)
                        
                        # Features
                        image_features = model.get_image_features(pixel_values=inputs['pixel_values'])
                        text_features = model.get_text_features(input_ids=inputs['input_ids'], 
                                                               attention_mask=inputs['attention_mask'])
                        
                        # Normalizza
                        image_features = image_features / image_features.norm(dim=1, keepdim=True)
                        text_features = text_features / text_features.norm(dim=1, keepdim=True)
                        
                        # Similarità
                        similarity = (image_features * text_features).sum().item()
                        scores.append(similarity)
                        
                        print(f"   🎯 CLIPScore: {similarity:.4f}")
                        
                    except Exception as e:
                        print(f"   ❌ Errore: {e}")
                else:
                    print(f"   ❌ Immagine non trovata")
    
    if scores:
        print(f"\n📊 RISULTATI:")
        print(f"   Scores individuali: {[f'{s:.4f}' for s in scores]}")
        print(f"   Score medio: {np.mean(scores):.4f}")
        print(f"   Min: {min(scores):.4f}, Max: {max(scores):.4f}")
    else:
        print("❌ Nessun score calcolato")

if __name__ == "__main__":
    test_clipscore()
