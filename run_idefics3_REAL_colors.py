#!/usr/bin/env python3
"""
Idefics3 Inference con dataset CORRETTO (immagini colorate)
Per esecuzione su boost
"""

import json
import torch
from PIL import Image
from transformers import AutoProcessor, AutoModelForVision2Seq
import os
from datetime import datetime

def load_model():
    """Carica il modello Idefics3"""
    print("🔄 Caricamento Idefics3...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    processor = AutoProcessor.from_pretrained("HuggingFaceM4/Idefics3-8B-Llama3")
    model = AutoModelForVision2Seq.from_pretrained(
        "HuggingFaceM4/Idefics3-8B-Llama3",
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
    ).to(device)
    
    print("✅ Idefics3 caricato!")
    return processor, model, device

def run_inference():
    """Esegue inferenza Idefics3 con dataset corretto"""
    
    # Dataset CORRETTO con immagini colorate
    dataset_path = "data/processed/xml_format_optimized/baseline_t7_corrected_400_REAL_colors_fixed.json"
    
    print(f"📂 Caricamento dataset CORRETTO: {dataset_path}")
    
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    print(f"✅ Dataset caricato: {len(dataset)} esempi")
    print("🎨 USANDO IMMAGINI COLORATE CORRETTE!")
    
    # Carica modello
    processor, model, device = load_model()
    
    # Risultati
    results = []
    
    print("🚀 Inizio inferenza Idefics3...")
    
    for i, item in enumerate(dataset):
        try:
            # Carica immagine COLORATA
            image_path = item['image_path']
            
            if not os.path.exists(image_path):
                print(f"❌ Immagine non trovata: {image_path}")
                continue
            
            image = Image.open(image_path).convert('RGB')
            
            # Prepara conversazione
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image"},
                        {"type": "text", "text": "Describe this image in detail."}
                    ]
                }
            ]
            
            # Prepara input
            prompt = processor.apply_chat_template(messages, add_generation_prompt=True)
            inputs = processor(text=prompt, images=[image], return_tensors="pt")
            
            # Sposta su device se necessario
            if torch.cuda.is_available():
                inputs = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}
            
            # Genera descrizione
            with torch.no_grad():
                generated_ids = model.generate(
                    **inputs,
                    max_new_tokens=500,
                    do_sample=False
                )
                
                prediction = processor.decode(
                    generated_ids[0][inputs['input_ids'].shape[1]:], 
                    skip_special_tokens=True
                )
            
            # Salva risultato
            result = {
                'example_id': i,
                'image_path': image_path,
                'ground_truth': item['caption'],
                'prediction': prediction,
                'success': True
            }
            
            results.append(result)
            
            if (i + 1) % 50 == 0:
                print(f"   📊 Processati: {i + 1}/{len(dataset)}")
            
        except Exception as e:
            print(f"❌ Errore esempio {i}: {e}")
            
            # Salva errore
            result = {
                'example_id': i,
                'image_path': item.get('image_path', ''),
                'ground_truth': item.get('caption', ''),
                'prediction': '',
                'success': False,
                'error': str(e)
            }
            results.append(result)
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/idefics3_REAL_colors_results_{timestamp}.json"
    
    os.makedirs("evaluation_results", exist_ok=True)
    
    output_data = {
        'model': 'Idefics3',
        'dataset': 'baseline_t7_corrected_400_REAL_colors_fixed.json',
        'timestamp': timestamp,
        'total_examples': len(dataset),
        'successful_examples': len([r for r in results if r['success']]),
        'results': results
    }
    
    with open(output_path, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\n✅ Idefics3 inference completata!")
    print(f"📁 Risultati salvati: {output_path}")
    print(f"📊 Esempi processati: {len([r for r in results if r['success']])}/{len(dataset)}")

if __name__ == "__main__":
    run_inference()
