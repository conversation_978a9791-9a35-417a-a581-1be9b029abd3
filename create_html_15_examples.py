#!/usr/bin/env python3
"""
Crea HTML completo con 15 esempi diversificati
"""

import json
import base64
import os
import random
from datetime import datetime

def load_all_data():
    """Carica tutti i dati necessari"""
    
    # Dataset originale
    with open("data/processed/xml_format_optimized/baseline_t7_corrected_400_REAL_colors_fixed.json", 'r') as f:
        dataset = json.load(f)
    
    # Risultati modelli
    with open("evaluation_results/blip2_REAL_colors_results_20250704_134206.json", 'r') as f:
        blip2_results = json.load(f)
    
    with open("evaluation_results/florence2_REAL_colors_results_20250704_173628.json", 'r') as f:
        florence2_results = json.load(f)
    
    with open("evaluation_results/idefics3_REAL_colors_100_results_20250704_182059.json", 'r') as f:
        idefics3_results = json.load(f)
    
    # Summary metriche
    with open("evaluation_results/baseline_models_summary_20250704_190423.json", 'r') as f:
        summary = json.load(f)
    
    return dataset, blip2_results, florence2_results, idefics3_results, summary

def image_to_base64(image_path):
    """Converte immagine in base64"""
    try:
        with open(image_path, 'rb') as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')
    except:
        return None

def select_diverse_examples(dataset, blip2_results, florence2_results, idefics3_results, num_examples=15):
    """Seleziona 15 esempi diversificati per la visualizzazione"""
    
    # Crea mapping per ID
    blip2_map = {r['example_id']: r for r in blip2_results['results'] if r['success']}
    florence2_map = {r['example_id']: r for r in florence2_results['results'] if r['success']}
    idefics3_map = {r['example_id']: r for r in idefics3_results['results'] if r['success']}
    
    # Trova esempi comuni (presenti in tutti i modelli)
    common_ids = set(blip2_map.keys()) & set(florence2_map.keys()) & set(idefics3_map.keys())
    
    print(f"📊 Esempi comuni disponibili: {len(common_ids)}")
    
    # Seleziona esempi casuali ma riproducibili
    random.seed(42)  # Per risultati consistenti
    selected_ids = random.sample(list(common_ids), min(num_examples, len(common_ids)))
    
    examples = []
    for example_id in selected_ids:
        # Trova esempio nel dataset originale
        dataset_example = None
        for ex in dataset:
            if dataset.index(ex) == example_id:
                dataset_example = ex
                break
        
        if dataset_example and os.path.exists(dataset_example['image_path']):
            example = {
                'id': example_id,
                'image_path': dataset_example['image_path'],
                'ground_truth': dataset_example['caption'],
                'blip2_prediction': blip2_map[example_id]['prediction'],
                'florence2_prediction': florence2_map[example_id]['prediction'],
                'idefics3_prediction': idefics3_map[example_id]['prediction']
            }
            examples.append(example)
    
    return examples

def create_complete_html_15(examples, summary, radar_image_path, output_path):
    """Crea HTML completo con 15 esempi"""
    
    # Carica radar chart
    with open(radar_image_path, 'rb') as img_file:
        radar_data = base64.b64encode(img_file.read()).decode('utf-8')
    
    # Estrai metriche
    models_data = {}
    for model_name, model_data in summary['models'].items():
        metrics = model_data['metrics']
        
        if model_name == "BLIP-2":
            normalized = {
                'BLEU-4': metrics['BLEU-4'],
                'METEOR': metrics['METEOR'], 
                'CIDEr': metrics['CIDEr'],
                'CLIPScore': metrics['CLIPScore']
            }
        elif model_name == "Florence2":
            normalized = {
                'BLEU-4': metrics['bleu4'],
                'METEOR': metrics['meteor'],
                'CIDEr': metrics['cider'], 
                'CLIPScore': metrics['clip_score']
            }
        elif model_name == "Idefics3":
            normalized = {
                'BLEU-4': metrics['bleu4'],
                'METEOR': metrics['meteor'],
                'CIDEr': metrics['cider'],
                'CLIPScore': metrics['clip_score']
            }
        
        models_data[model_name] = {
            'metrics': normalized,
            'num_examples': model_data['num_examples']
        }
    
    # Trova vincitori
    metrics = ['BLEU-4', 'METEOR', 'CIDEr', 'CLIPScore']
    winners = {}
    for metric in metrics:
        best_model = max(models_data.keys(), 
                        key=lambda m: models_data[m]['metrics'][metric])
        winners[metric] = best_model
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Confronto Completo Modelli Baseline - 15 Esempi</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #333;
            }}
            .container {{
                max-width: 1400px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                overflow: hidden;
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }}
            .header h1 {{
                margin: 0;
                font-size: 2.5em;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }}
            .content {{
                padding: 30px;
            }}
            .section {{
                margin: 40px 0;
                padding: 20px;
                border-radius: 10px;
                background: #f8f9fa;
            }}
            .section h2 {{
                color: #495057;
                border-bottom: 3px solid #007bff;
                padding-bottom: 10px;
            }}
            .radar-section {{
                text-align: center;
            }}
            .radar-image {{
                max-width: 100%;
                height: auto;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }}
            .metrics-summary {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }}
            .metric-card {{
                background: white;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }}
            .metric-name {{
                font-weight: bold;
                color: #007bff;
                font-size: 1.1em;
            }}
            .metric-winner {{
                color: #28a745;
                font-weight: bold;
                margin-top: 5px;
            }}
            .examples-grid {{
                display: grid;
                grid-template-columns: 1fr;
                gap: 25px;
            }}
            .example-card {{
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 3px 10px rgba(0,0,0,0.1);
                border-left: 5px solid #007bff;
            }}
            .example-header {{
                display: flex;
                align-items: flex-start;
                gap: 20px;
                margin-bottom: 20px;
            }}
            .example-image {{
                width: 120px;
                height: 120px;
                object-fit: contain;
                border-radius: 8px;
                border: 2px solid #dee2e6;
                background: white;
                flex-shrink: 0;
            }}
            .example-info {{
                flex: 1;
            }}
            .example-id {{
                font-size: 0.9em;
                color: #6c757d;
                margin-bottom: 10px;
            }}
            .ground-truth {{
                background: #e7f3ff;
                padding: 12px;
                border-radius: 5px;
                border-left: 4px solid #007bff;
                margin-bottom: 15px;
                font-weight: 500;
            }}
            .predictions {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 12px;
            }}
            .prediction {{
                padding: 12px;
                border-radius: 5px;
                border-left: 4px solid;
                font-size: 0.95em;
            }}
            .prediction.blip2 {{
                background: #ffe7e7;
                border-left-color: #FF6B6B;
            }}
            .prediction.florence2 {{
                background: #e7f9f7;
                border-left-color: #4ECDC4;
            }}
            .prediction.idefics3 {{
                background: #e7f1ff;
                border-left-color: #45B7D1;
            }}
            .prediction-label {{
                font-weight: bold;
                margin-bottom: 5px;
                font-size: 0.9em;
            }}
            .stats-banner {{
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px;
                border-radius: 10px;
                text-align: center;
                margin: 20px 0;
                font-weight: bold;
            }}
            .timestamp {{
                text-align: center;
                color: #6c757d;
                font-style: italic;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #dee2e6;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>CONFRONTO COMPLETO MODELLI BASELINE</h1>
                <p>Analisi Quantitativa e Qualitativa sui Colored Images Dataset</p>
                <div class="stats-banner">
                    📊 {len(examples)} Esempi Diversificati con Immagini Reali
                </div>
            </div>
            
            <div class="content">
                <div class="section radar-section">
                    <h2>📊 Performance Comparative</h2>
                    <img src="data:image/png;base64,{radar_data}" alt="Radar Chart" class="radar-image">
                    <p><em>Scale appropriate per metrica: BLEU-4 (0-0.1), METEOR (0-0.5), CIDEr (0-2.0), CLIPScore (0-40)</em></p>
                    
                    <div class="metrics-summary">
    """
    
    # Aggiungi summary metriche
    for metric in metrics:
        winner = winners[metric]
        value = models_data[winner]['metrics'][metric]
        
        if metric == 'CLIPScore':
            value_str = f"{value:.2f}"
        else:
            value_str = f"{value:.4f}"
        
        html_content += f"""
                        <div class="metric-card">
                            <div class="metric-name">{metric}</div>
                            <div class="metric-winner">{winner}</div>
                            <div>{value_str}</div>
                        </div>
        """
    
    html_content += """
                    </div>
                </div>

                <div class="section">
                    <h2>🖼️ Esempi Qualitativi (15 Esempi Diversificati)</h2>
                    <p>Confronto delle caption generate dai tre modelli baseline su esempi selezionati dal dataset:</p>

                    <div class="examples-grid">
    """

    # Aggiungi tutti i 15 esempi
    for i, example in enumerate(examples):
        # Converti immagine in base64
        img_data = image_to_base64(example['image_path'])
        if not img_data:
            continue

        html_content += f"""
                        <div class="example-card">
                            <div class="example-header">
                                <img src="data:image/png;base64,{img_data}" alt="Example {i+1}" class="example-image">
                                <div class="example-info">
                                    <div class="example-id">Esempio #{i+1} (ID: {example['id']})</div>
                                    <div class="ground-truth">
                                        <strong>🎯 Ground Truth:</strong><br>
                                        {example['ground_truth']}
                                    </div>
                                </div>
                            </div>

                            <div class="predictions">
                                <div class="prediction blip2">
                                    <div class="prediction-label">🔴 BLIP-2:</div>
                                    {example['blip2_prediction']}
                                </div>
                                <div class="prediction florence2">
                                    <div class="prediction-label">🟢 Florence2:</div>
                                    {example['florence2_prediction']}
                                </div>
                                <div class="prediction idefics3">
                                    <div class="prediction-label">🔵 Idefics3:</div>
                                    {example['idefics3_prediction']}
                                </div>
                            </div>
                        </div>
        """

    # Chiudi HTML
    timestamp_str = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
    html_content += f"""
                    </div>
                </div>

                <div class="timestamp">
                    Report completo generato il {timestamp_str}<br>
                    Include: Radar Chart Corretto + <strong>{len(examples)} Esempi Qualitativi</strong> con Immagini Embedded<br>
                    Dataset: baseline_t7_corrected_400_REAL_colors_fixed.json
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    # Salva HTML
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"✅ HTML completo con {len(examples)} esempi salvato: {output_path}")
    return html_content

def main():
    """Genera HTML completo con 15 esempi"""

    print("🔧 CREAZIONE HTML COMPLETO CON 15 ESEMPI")
    print("=" * 50)

    # Carica dati
    dataset, blip2_results, florence2_results, idefics3_results, summary = load_all_data()

    # Seleziona 15 esempi diversificati
    examples = select_diverse_examples(dataset, blip2_results, florence2_results, idefics3_results, 15)
    print(f"📊 Selezionati {len(examples)} esempi con immagini valide")

    # Radar chart corretto
    radar_path = "evaluation_results/baseline_radar_CORRECT_20250704_204208.png"

    # Timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Genera HTML
    html_path = f"evaluation_results/baseline_complete_15_examples_{timestamp}.html"
    create_complete_html_15(examples, summary, radar_path, html_path)

    print("\n🎉 HTML COMPLETO CON 15 ESEMPI GENERATO!")
    print("=" * 50)
    print(f"📄 File: {html_path}")
    print(f"📊 Include: Radar Chart + {len(examples)} Esempi Diversificati")
    print("✅ Tutte le immagini embedded in base64")
    print("🎯 Layout ottimizzato per 15 esempi")

if __name__ == "__main__":
    main()
