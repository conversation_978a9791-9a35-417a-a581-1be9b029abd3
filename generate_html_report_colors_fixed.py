#!/usr/bin/env python3
"""
Genera report HTML qualitativo per baseline con dataset colori corretti
Mostra esempi rappresentativi con immagini, ground truth e predizioni
"""

import json
import os
import random
import base64
from datetime import datetime
from pathlib import Path

def load_baseline_results():
    """Carica risultati baseline con dataset colori corretti"""
    results = {}
    
    # Path dei risultati
    paths = {
        "BLIP-2": "evaluation_results/baseline_colors_fixed_new/blip2_results.json",
        "Florence2": "evaluation_results/baseline_colors_fixed_new/florence2_results.json", 
        "Idefics3": "evaluation_results/idefics3_only_colors_fixed/idefics3_results.json"
    }
    
    for model_name, path in paths.items():
        if os.path.exists(path):
            with open(path, 'r') as f:
                data = json.load(f)
                # Estrai i risultati dalla struttura JSON
                if isinstance(data, dict) and 'results' in data:
                    results[model_name] = data['results']
                    print(f"✅ {model_name}: {len(data['results'])} esempi caricati")
                elif isinstance(data, list):
                    results[model_name] = data
                    print(f"✅ {model_name}: {len(data)} esempi caricati")
        else:
            print(f"❌ File non trovato: {path}")
    
    return results

def find_image_path(example_id):
    """Trova il path dell'immagine per un dato example_id"""
    # Cerca nelle directory delle immagini
    image_dirs = [
        "data/processed/xml_format_optimized/images",
        "data/processed/images", 
        "data/images"
    ]
    
    for img_dir in image_dirs:
        if os.path.exists(img_dir):
            # Prova diversi formati di nome file
            possible_names = [
                f"example_{example_id:03d}.png",
                f"example_{example_id}.png",
                f"{example_id:03d}.png",
                f"{example_id}.png"
            ]
            
            for name in possible_names:
                img_path = os.path.join(img_dir, name)
                if os.path.exists(img_path):
                    return img_path
    
    return None

def image_to_base64(image_path):
    """Converte immagine in base64 per embedding HTML"""
    if not image_path or not os.path.exists(image_path):
        return None
    
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
            return base64.b64encode(image_data).decode('utf-8')
    except Exception as e:
        print(f"⚠️ Errore caricamento immagine {image_path}: {e}")
        return None

def select_diverse_examples(results, num_examples=10):
    """Seleziona esempi diversi per il report"""
    if len(results) <= num_examples:
        return results
    
    # Seleziona esempi distribuiti uniformemente
    step = len(results) // num_examples
    selected = []
    
    for i in range(0, len(results), step):
        if len(selected) < num_examples:
            selected.append(results[i])
    
    return selected

def create_html_report(baseline_results, output_file):
    """Crea report HTML qualitativo"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baseline Models - Qualitative Report (Dataset Colori Corretti)</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }}
        .model-section {{
            margin: 30px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }}
        .model-header {{
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
        }}
        .model-header h2 {{
            margin: 0;
            color: #495057;
            font-size: 1.8em;
        }}
        .stats {{
            background: #e3f2fd;
            padding: 15px;
            margin-top: 10px;
            border-radius: 5px;
            font-weight: 500;
        }}
        .example {{
            display: flex;
            margin: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }}
        .image-container {{
            flex: 0 0 300px;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }}
        .example-id {{
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            margin-bottom: 10px;
        }}
        .image-container img {{
            max-width: 100%;
            max-height: 200px;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        .text-container {{
            flex: 1;
            padding: 20px;
        }}
        .ground-truth {{
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 5px 5px 0;
        }}
        .prediction {{
            border-left: 4px solid #007bff;
            padding: 15px;
            border-radius: 0 5px 5px 0;
        }}
        .prediction.blip2 {{ background: #fff3cd; border-left-color: #ffc107; }}
        .prediction.florence2 {{ background: #d1ecf1; border-left-color: #17a2b8; }}
        .prediction.idefics3 {{ background: #f8d7da; border-left-color: #dc3545; }}
        .label {{
            font-weight: bold;
            margin-bottom: 8px;
            display: block;
        }}
        .footer {{
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }}
        .no-image {{
            background: #e9ecef;
            color: #6c757d;
            padding: 40px;
            text-align: center;
            border-radius: 5px;
            font-style: italic;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Baseline Models - Qualitative Report</h1>
            <p>Dataset: baseline_t7_corrected_400_colors_fixed.json</p>
            <p>Generated: {timestamp}</p>
        </div>
"""

    # Genera sezioni per ogni modello
    for model_name, results in baseline_results.items():
        if not results:
            continue
        
        # Seleziona esempi diversi
        selected_examples = select_diverse_examples(results, 10)
        
        model_class = model_name.lower().replace('-', '').replace('_', '')
        
        html_content += f"""
        <div class="model-section">
            <div class="model-header">
                <h2>{model_name}</h2>
                <div class="stats">
                    📊 Showing {len(selected_examples)} representative examples out of {len(results)} total
                    | Success Rate: {len([r for r in results if r.get('success', True)])/len(results)*100:.1f}%
                </div>
            </div>
"""
        
        # Genera esempi
        for example in selected_examples:
            example_id = example.get('example_id', 'N/A')
            prediction = example.get('prediction', 'No prediction available')
            ground_truth = example.get('ground_truth', 'No ground truth available')
            
            # Trova immagine
            image_path = find_image_path(example_id)
            image_b64 = image_to_base64(image_path) if image_path else None
            
            html_content += f"""
            <div class="example">
                <div class="image-container">
                    <div class="example-id">Example {example_id}</div>
"""
            
            if image_b64:
                html_content += f'<img src="data:image/png;base64,{image_b64}" alt="Example {example_id}">'
            else:
                html_content += '<div class="no-image">Image not found</div>'
            
            html_content += f"""
                </div>
                <div class="text-container">
                    <div class="ground-truth">
                        <span class="label">🎯 Ground Truth:</span>
                        {ground_truth}
                    </div>
                    <div class="prediction {model_class}">
                        <span class="label">🤖 {model_name} Prediction:</span>
                        {prediction}
                    </div>
                </div>
            </div>
"""
        
        html_content += "        </div>\n"
    
    html_content += f"""
        <div class="footer">
            <p>📝 Report generated automatically from baseline model results</p>
            <p>🕒 Timestamp: {timestamp}</p>
            <p>🔬 Models evaluated: {', '.join(baseline_results.keys())}</p>
            <p>📊 Dataset: baseline_t7_corrected_400_colors_fixed.json (400 examples)</p>
        </div>
    </div>
</body>
</html>"""
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Report HTML generato: {output_file}")

def main():
    print("🎨 GENERAZIONE REPORT HTML QUALITATIVO - DATASET COLORI CORRETTI")
    print("=" * 70)
    
    # Carica risultati baseline
    baseline_results = load_baseline_results()
    
    if not baseline_results:
        print("❌ Nessun risultato baseline trovato!")
        return
    
    # Genera report HTML
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"evaluation_results/baseline_qualitative_report_colors_fixed_{timestamp}.html"
    
    create_html_report(baseline_results, output_file)
    
    print(f"\n🎉 Report HTML completato!")
    print(f"📁 File: {output_file}")
    print(f"\n💡 Per visualizzare:")
    print(f"   firefox {output_file}")

if __name__ == "__main__":
    main()
