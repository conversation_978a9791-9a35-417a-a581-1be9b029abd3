#!/usr/bin/env python3
"""
Script per aprire il report HTML nel browser
"""

import webbrowser
import os
import argparse

def main():
    parser = argparse.ArgumentParser(description='Apri report HTML nel browser')
    parser.add_argument('--file', default='evaluation_results/baseline_qualitative_report_complete.html',
                       help='File HTML da aprire')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.file):
        print(f"❌ File non trovato: {args.file}")
        return
    
    # Converti in percorso assoluto
    abs_path = os.path.abspath(args.file)
    file_url = f"file://{abs_path}"
    
    print(f"🌐 Apertura report nel browser...")
    print(f"📁 File: {abs_path}")
    
    try:
        webbrowser.open(file_url)
        print("✅ Report aperto nel browser!")
    except Exception as e:
        print(f"❌ Errore apertura browser: {e}")
        print(f"🔗 URL manuale: {file_url}")

if __name__ == "__main__":
    main()
