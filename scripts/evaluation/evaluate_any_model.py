#!/usr/bin/env python3
"""
Script generico per valutare qualsiasi modello con tutte le metriche
e generare grafico radar automaticamente.

Usage:
    python evaluate_any_model.py --results_file path/to/model_results.json --model_name "MyModel" --output_dir evaluation_results/
"""

import json
import os
import sys
import argparse
import logging
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
from math import pi

# Aggiungi path per le metriche
sys.path.append('/work/tesi_ediluzio')

# Import diretto per evitare problemi con tensorboardX
import importlib.util
spec = importlib.util.spec_from_file_location("metrics", "/work/tesi_ediluzio/shared/utils/metrics.py")
metrics_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(metrics_module)
CaptionEvaluator = metrics_module.CaptionEvaluator

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_dataset_mapping():
    """Carica la mappatura tra example_id e image_path dal dataset originale"""
    dataset_file = "/work/tesi_ediluzio/data/processed/xml_format_optimized/baseline_t7_corrected_400_with_images.json"
    try:
        with open(dataset_file, 'r') as f:
            dataset = json.load(f)
        
        mapping = {}
        for i, example in enumerate(dataset):
            mapping[i] = example.get('image_path', '')
        
        logger.info(f"📊 Caricata mappatura per {len(mapping)} esempi")
        return mapping
    except Exception as e:
        logger.error(f"❌ Errore caricamento dataset: {e}")
        return {}

def extract_data_for_metrics(model_results):
    """Estrae predictions e ground_truth per calcolo metriche"""
    predictions = []
    references = []
    image_paths = []
    
    dataset_mapping = load_dataset_mapping()
    
    for result in model_results:
        if result.get('success', False):
            predictions.append(result['prediction'])
            references.append([result['ground_truth']])
            
            if 'image_path' in result:
                image_paths.append(result['image_path'])
            elif 'example_id' in result:
                example_id = result['example_id']
                image_path = dataset_mapping.get(example_id, '')
                if image_path and os.path.exists(image_path):
                    image_paths.append(image_path)
                else:
                    logger.warning(f"⚠️ Immagine non trovata per example_id {example_id}")
                    image_paths.append("")
            else:
                image_paths.append("")
    
    return predictions, references, image_paths

def calculate_diversity(predictions):
    """Calcola diversità delle predizioni"""
    unique_predictions = len(set(predictions))
    total_predictions = len(predictions)
    diversity_rate = unique_predictions / total_predictions if total_predictions > 0 else 0
    
    return {
        'diversity_rate': diversity_rate,
        'unique_predictions': unique_predictions,
        'total_predictions': total_predictions
    }

def evaluate_model(results_file, model_name):
    """Valuta un modello con tutte le metriche"""
    logger.info(f"🚀 Valutazione modello: {model_name}")
    
    # Carica risultati
    with open(results_file, 'r') as f:
        data = json.load(f)

    # Gestisci diversi formati di file
    if isinstance(data, list):
        model_results = data
    elif isinstance(data, dict) and 'results' in data:
        model_results = data['results']
    elif isinstance(data, dict) and 'predictions' in data:
        model_results = data['predictions']
    else:
        logger.error(f"❌ Formato file non riconosciuto: {results_file}")
        return None

    logger.info(f"✅ {model_name}: {len(model_results)} esempi caricati")
    
    # Estrai dati per metriche
    predictions, references, image_paths = extract_data_for_metrics(model_results)
    
    if not predictions:
        logger.error(f"❌ Nessuna predizione valida trovata per {model_name}")
        return None
    
    # Inizializza evaluator
    evaluator = CaptionEvaluator()
    
    # Calcola metriche
    logger.info(f"📊 Calcolo metriche per {model_name}...")
    metrics = evaluator.evaluate_all(references, predictions, image_paths)
    
    # Calcola diversità
    diversity = calculate_diversity(predictions)
    
    # Statistiche successo
    total_count = len(model_results)
    success_count = len(predictions)
    success_rate = success_count / total_count if total_count > 0 else 0
    
    # Combina risultati
    model_metrics = {
        'model_name': model_name,
        'total_examples': total_count,
        'successful_examples': success_count,
        'success_rate': success_rate,
        'bleu1': metrics['bleu1'],
        'bleu2': metrics['bleu2'], 
        'bleu3': metrics['bleu3'],
        'bleu4': metrics['bleu4'],
        'rouge1': metrics['rouge1'],
        'rouge2': metrics['rouge2'],
        'rougeL': metrics['rougeL'],
        'meteor': metrics['meteor'],
        'cider': metrics['cider'],
        'clip_score': metrics['clip_score'],
        'diversity_rate': diversity['diversity_rate'],
        'unique_predictions': diversity['unique_predictions']
    }
    
    # Log risultati
    logger.info(f"✅ {model_name} metriche:")
    logger.info(f"  Success Rate: {success_count}/{total_count} ({success_rate:.1%})")
    logger.info(f"  BLEU-4: {metrics['bleu4']:.4f}")
    logger.info(f"  METEOR: {metrics['meteor']:.4f}")
    logger.info(f"  CIDEr: {metrics['cider']:.4f}")
    logger.info(f"  CLIPScore: {metrics['clip_score']:.4f}")
    logger.info(f"  ROUGE-L: {metrics['rougeL']:.4f}")
    logger.info(f"  Diversità: {diversity['diversity_rate']:.3f}")
    
    return model_metrics

def create_radar_chart(metrics_data, output_path, title="Model Performance"):
    """Crea grafico radar con le metriche"""
    
    metric_names = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIPScore']
    
    # Estrai dati per ogni modello e normalizza CLIPScore (0-100 -> 0-1)
    models_data = {}
    models_raw_data = {}
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
    
    for i, (model_name, model_metrics) in enumerate(metrics_data.items()):
        raw_values = [
            model_metrics['bleu1'],
            model_metrics['bleu2'], 
            model_metrics['bleu3'],
            model_metrics['bleu4'],
            model_metrics['meteor'],
            model_metrics['cider'],
            model_metrics['clip_score']
        ]
        
        # Normalizza CLIPScore da 0-100 a 0-1 per il grafico
        normalized_values = raw_values.copy()
        normalized_values[6] = raw_values[6] / 100.0  # CLIPScore
        
        models_data[model_name] = normalized_values
        models_raw_data[model_name] = raw_values
    
    # Setup del grafico
    fig, ax = plt.subplots(figsize=(16, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = [n / float(len(metric_names)) * 2 * pi for n in range(len(metric_names))]
    angles += angles[:1]  # Chiudi il cerchio
    
    # Plotta ogni modello con valori nella legenda
    legend_labels = []
    for i, (model_name, values) in enumerate(models_data.items()):
        values += values[:1]  # Chiudi il cerchio
        raw_values = models_raw_data[model_name]
        
        # Crea label con valori principali
        label_text = f"{model_name}\n(BLEU-4: {raw_values[3]:.3f}, METEOR: {raw_values[4]:.3f}, CLIPScore: {raw_values[6]:.1f})"
        legend_labels.append(label_text)
        
        color = colors[i % len(colors)]
        ax.plot(angles, values, 'o-', linewidth=2, color=color)
        ax.fill(angles, values, alpha=0.25, color=color)
    
    # Personalizza il grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_names, fontsize=12, fontweight='bold')
    
    # Imposta i limiti radiali
    ax.set_ylim(0, 1.0)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=10)
    ax.grid(True)
    
    # Titolo e legenda con valori
    total_examples = list(metrics_data.values())[0]["total_examples"] if metrics_data else 0
    plt.title(f'{title}\n({total_examples} Examples)',
              size=16, fontweight='bold', pad=20)
    
    plt.legend(legend_labels, loc='upper right', bbox_to_anchor=(1.5, 1.0), fontsize=10)
    
    # Aggiungi tabella con valori sulla sinistra
    table_data = []
    table_data.append(['Metric'] + list(models_raw_data.keys()))
    
    for i, metric in enumerate(metric_names):
        row = [metric]
        for model_name in models_raw_data.keys():
            value = models_raw_data[model_name][i]
            if metric == 'CLIPScore':
                row.append(f'{value:.1f}')
            else:
                row.append(f'{value:.4f}')
        table_data.append(row)
    
    # Posiziona tabella
    table_text = ""
    for row in table_data:
        table_text += f"{'  '.join(f'{cell:<12}' for cell in row)}\n"
    
    plt.figtext(0.02, 0.5, table_text, fontsize=9, fontfamily='monospace', 
                verticalalignment='center', bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    # Aggiungi note
    note_text = "BLEU-1,2,3,4, METEOR, CIDEr: Higher is better | CLIPScore: Image-text similarity (0-100)"
    plt.figtext(0.5, 0.02, note_text, ha='center', fontsize=10, style='italic')
    
    # Salva
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.savefig(output_path.replace('.png', '.pdf'), dpi=300, bbox_inches='tight')
    
    logger.info(f"✅ Grafico radar salvato: {output_path}")
    logger.info(f"✅ Grafico radar PDF: {output_path.replace('.png', '.pdf')}")

def main():
    parser = argparse.ArgumentParser(description='Valuta qualsiasi modello con tutte le metriche')
    parser.add_argument('--results_file', required=True, help='File JSON con i risultati del modello')
    parser.add_argument('--model_name', required=True, help='Nome del modello')
    parser.add_argument('--output_dir', default='evaluation_results/', help='Directory di output')
    parser.add_argument('--compare_with', help='File JSON con metriche di altri modelli per confronto')
    
    args = parser.parse_args()
    
    # Crea directory output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Valuta il modello
    model_metrics = evaluate_model(args.results_file, args.model_name)
    
    if model_metrics is None:
        logger.error("❌ Valutazione fallita")
        return
    
    # Prepara dati per grafico
    all_metrics = {args.model_name: model_metrics}
    
    # Se richiesto, carica altri modelli per confronto
    if args.compare_with:
        try:
            with open(args.compare_with, 'r') as f:
                comparison_data = json.load(f)
                if 'metrics' in comparison_data:
                    all_metrics.update(comparison_data['metrics'])
                else:
                    all_metrics.update(comparison_data)
            logger.info(f"📊 Caricati {len(comparison_data)} modelli per confronto")
        except Exception as e:
            logger.warning(f"⚠️ Errore caricamento confronto: {e}")
    
    # Salva metriche
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    metrics_file = os.path.join(args.output_dir, f"{args.model_name}_metrics_{timestamp}.json")
    
    output_data = {
        'timestamp': timestamp,
        'model_evaluated': args.model_name,
        'dataset_size': model_metrics['total_examples'],
        'metrics': all_metrics
    }
    
    with open(metrics_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    logger.info(f"💾 Metriche salvate: {metrics_file}")
    
    # Crea grafico radar
    chart_file = os.path.join(args.output_dir, f"{args.model_name}_radar_chart_{timestamp}.png")
    title = f"{args.model_name} Performance" if len(all_metrics) == 1 else "Models Comparison"
    
    create_radar_chart(all_metrics, chart_file, title)
    
    # Stampa riassunto finale
    print("\n" + "="*80)
    print(f"🎉 VALUTAZIONE COMPLETATA: {args.model_name}")
    print("="*80)
    print(f"📁 Metriche: {metrics_file}")
    print(f"📊 Grafico: {chart_file}")
    print(f"📈 Modelli confrontati: {len(all_metrics)}")
    print("="*80)

if __name__ == "__main__":
    main()
