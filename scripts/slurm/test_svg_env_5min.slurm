#!/bin/bash
#SBATCH --job-name=test_svg_env_5min
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/test_svg_env_5min_%j.out
#SBATCH --error=logs/test_svg_env_5min_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=00:10:00
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

echo "🧪 TEST SVG_ENV_NEW 5 MINUTI - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente CORRETTO
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "🔍 Verifica ambiente svg_env_new..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HF_HUB_DISABLE_TELEMETRY=1
export DISABLE_MLFLOW_INTEGRATION=TRUE
export TRANSFORMERS_OFFLINE=0
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/test_svg_env_5min"

echo "📂 Output directory: $OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

echo "✅ Avvio TEST SVG_ENV_NEW 5 MINUTI..."

# Lancia training con ambiente corretto (5 minuti = 300 secondi)
timeout 300s /homes/ediluzio/.conda/envs/svg_env_new/bin/python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path "google/gemma-2-2b-it" \
    --data_file "data/processed/xml_format_optimized/train_set_100k_final_90000.json" \
    --config_path "experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json" \
    --output_dir "$OUTPUT_DIR"

EXIT_CODE=$?

echo ""
echo "🏁 TEST SVG_ENV_NEW 5 MINUTI COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 124 ]; then
    echo "✅ Test terminato correttamente dopo 5 minuti (timeout)"
elif [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Test completato normalmente"
else
    echo "❌ Test fallito con codice: $EXIT_CODE"
fi

# Verifica output
if [ -d "$OUTPUT_DIR" ]; then
    echo "📁 Output directory creata:"
    ls -la "$OUTPUT_DIR"
    
    # Verifica checkpoint
    if [ -d "$OUTPUT_DIR/checkpoint-"* ]; then
        echo "✅ Checkpoint creati con successo!"
        find "$OUTPUT_DIR" -name "checkpoint-*" -type d
    else
        echo "⚠️ Nessun checkpoint trovato"
    fi
else
    echo "❌ Output directory non trovata"
fi
