#!/usr/bin/env python3
"""
Calcola metriche complete per baseline 100 esempi: BLEU, CIDEr, ROUGE, METEOR
"""

import json
import os
import sys
import logging
from pathlib import Path
import numpy as np

# Aggiungi path per le metriche
sys.path.append('/work/tesi_ediluzio')

# Import diretto per evitare problemi con tensorboardX
import importlib.util
spec = importlib.util.spec_from_file_location("metrics", "/work/tesi_ediluzio/shared/utils/metrics.py")
metrics_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(metrics_module)
CaptionEvaluator = metrics_module.CaptionEvaluator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_baseline_results():
    """Carica risultati baseline da evaluation_results/baseline_optimized/"""
    base_path = "/work/tesi_ediluzio/evaluation_results/baseline_optimized"
    
    results = {}
    
    # File da caricare
    files = {
        "IDEFICS3": "idefics3_results.json",
        "Florence2": "florence2_results.json", 
        "BLIP-2": "blip2_results.json"
    }
    
    for model_name, filename in files.items():
        file_path = os.path.join(base_path, filename)
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                data = json.load(f)
                results[model_name] = data['results']
                logger.info(f"✅ {model_name}: {len(data['results'])} esempi caricati")
        else:
            logger.error(f"❌ File non trovato: {file_path}")
    
    return results

def load_dataset_mapping():
    """Carica la mappatura tra example_id e image_path dal dataset originale"""
    dataset_file = "/work/tesi_ediluzio/data/processed/xml_format_optimized/baseline_t7_corrected_400_with_images.json"
    try:
        with open(dataset_file, 'r') as f:
            dataset = json.load(f)

        # Crea mappatura example_id -> image_path
        mapping = {}
        for i, example in enumerate(dataset):
            mapping[i] = example.get('image_path', '')

        logger.info(f"📊 Caricata mappatura per {len(mapping)} esempi")
        return mapping
    except Exception as e:
        logger.error(f"❌ Errore caricamento dataset: {e}")
        return {}

def extract_data_for_metrics(model_results):
    """Estrae predictions e ground_truth per calcolo metriche"""
    predictions = []
    references = []
    image_paths = []

    # Carica mappatura dataset
    dataset_mapping = load_dataset_mapping()

    for result in model_results:
        if result.get('success', False):
            predictions.append(result['prediction'])
            # References come lista per compatibilità con CIDEr
            references.append([result['ground_truth']])

            # Ottieni image path dalla mappatura
            if 'image_path' in result:
                image_paths.append(result['image_path'])
            elif 'example_id' in result:
                example_id = result['example_id']
                image_path = dataset_mapping.get(example_id, '')
                if image_path and os.path.exists(image_path):
                    image_paths.append(image_path)
                else:
                    logger.warning(f"⚠️ Immagine non trovata per example_id {example_id}")
                    image_paths.append("")
            else:
                image_paths.append("")

    return predictions, references, image_paths

def calculate_diversity(predictions):
    """Calcola diversità delle predizioni"""
    unique_predictions = set(predictions)
    diversity_rate = len(unique_predictions) / len(predictions)
    return {
        'unique_predictions': len(unique_predictions),
        'total_predictions': len(predictions),
        'diversity_rate': diversity_rate
    }

def main():
    logger.info("🚀 Calcolo metriche baseline 100 esempi...")
    
    # Carica risultati
    baseline_results = load_baseline_results()
    
    if not baseline_results:
        logger.error("❌ Nessun risultato baseline trovato!")
        return
    
    # Inizializza evaluator
    evaluator = CaptionEvaluator()
    
    # Risultati finali
    final_metrics = {}
    
    # Calcola metriche per ogni modello
    for model_name, model_results in baseline_results.items():
        logger.info(f"📊 Calcolo metriche per {model_name}...")
        
        # Estrai dati
        predictions, references, image_paths = extract_data_for_metrics(model_results)
        
        if not predictions:
            logger.warning(f"⚠️ Nessuna predizione valida per {model_name}")
            continue
        
        # Calcola tutte le metriche
        metrics = evaluator.evaluate_all(references, predictions, image_paths)
        
        # Calcola diversità
        diversity = calculate_diversity(predictions)
        
        # Aggiungi statistiche
        success_count = len(predictions)
        total_count = len(model_results)
        success_rate = success_count / total_count if total_count > 0 else 0
        
        # Combina risultati
        model_metrics = {
            'model_name': model_name,
            'total_examples': total_count,
            'successful_examples': success_count,
            'success_rate': success_rate,
            'bleu1': metrics['bleu1'],
            'bleu2': metrics['bleu2'],
            'bleu3': metrics['bleu3'],
            'bleu4': metrics['bleu4'],
            'rouge1': metrics['rouge1'],
            'rouge2': metrics['rouge2'],
            'rougeL': metrics['rougeL'],
            'meteor': metrics['meteor'],
            'cider': metrics['cider'],
            'clip_score': metrics['clip_score'],
            'diversity_rate': diversity['diversity_rate'],
            'unique_predictions': diversity['unique_predictions']
        }
        
        final_metrics[model_name] = model_metrics
        
        # Log risultati
        logger.info(f"✅ {model_name} metriche:")
        logger.info(f"  Success Rate: {success_count}/{total_count} ({success_rate:.1%})")
        logger.info(f"  BLEU-4: {metrics['bleu4']:.4f}")
        logger.info(f"  METEOR: {metrics['meteor']:.4f}")
        logger.info(f"  CIDEr: {metrics['cider']:.4f}")
        logger.info(f"  CLIPScore: {metrics['clip_score']:.4f}")
        logger.info(f"  ROUGE-L: {metrics['rougeL']:.4f}")
        logger.info(f"  Diversità: {diversity['diversity_rate']:.3f}")
        print()
    
    # Salva risultati
    output_file = "/work/tesi_ediluzio/evaluation_results/baseline_optimized/baseline_metrics_complete.json"
    
    final_results = {
        'timestamp': '2025-07-02',
        'dataset_size': 100,
        'models_evaluated': list(final_metrics.keys()),
        'metrics': final_metrics
    }
    
    with open(output_file, 'w') as f:
        json.dump(final_results, f, indent=2)
    
    logger.info(f"💾 Risultati salvati in: {output_file}")
    
    # Stampa tabella riassuntiva
    print("\n" + "="*100)
    print("📊 BASELINE METRICS SUMMARY (100 esempi)")
    print("="*100)
    print(f"{'Modello':<12} {'BLEU-4':<8} {'METEOR':<8} {'CIDEr':<8} {'CLIPScore':<10} {'Success':<8} {'Diversità':<10}")
    print("-"*100)

    for model_name, metrics in final_metrics.items():
        print(f"{model_name:<12} {metrics['bleu4']:<8.4f} {metrics['meteor']:<8.4f} {metrics['cider']:<8.4f} "
              f"{metrics['clip_score']:<10.4f} {metrics['success_rate']:<8.1%} {metrics['diversity_rate']:<10.3f}")

    print("="*100)
    logger.info("🎉 Calcolo metriche completato!")

if __name__ == "__main__":
    main()
